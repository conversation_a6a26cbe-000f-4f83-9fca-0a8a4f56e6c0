{"version": 3, "sources": ["../../../../../node_modules/.pnpm/react-dom@19.1.1_react@19.1.1/node_modules/react-dom/cjs/react-dom.production.js", "../../../../../node_modules/.pnpm/react-dom@19.1.1_react@19.1.1/node_modules/react-dom/index.js"], "sourcesContent": ["/**\n * @license React\n * react-dom.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\");\nfunction formatProdErrorMessage(code) {\n  var url = \"https://react.dev/errors/\" + code;\n  if (1 < arguments.length) {\n    url += \"?args[]=\" + encodeURIComponent(arguments[1]);\n    for (var i = 2; i < arguments.length; i++)\n      url += \"&args[]=\" + encodeURIComponent(arguments[i]);\n  }\n  return (\n    \"Minified React error #\" +\n    code +\n    \"; visit \" +\n    url +\n    \" for the full message or use the non-minified dev environment for full errors and additional helpful warnings.\"\n  );\n}\nfunction noop() {}\nvar Internals = {\n    d: {\n      f: noop,\n      r: function () {\n        throw Error(formatProdErrorMessage(522));\n      },\n      D: noop,\n      C: noop,\n      L: noop,\n      m: noop,\n      X: noop,\n      S: noop,\n      M: noop\n    },\n    p: 0,\n    findDOMNode: null\n  },\n  REACT_PORTAL_TYPE = Symbol.for(\"react.portal\");\nfunction createPortal$1(children, containerInfo, implementation) {\n  var key =\n    3 < arguments.length && void 0 !== arguments[3] ? arguments[3] : null;\n  return {\n    $$typeof: REACT_PORTAL_TYPE,\n    key: null == key ? null : \"\" + key,\n    children: children,\n    containerInfo: containerInfo,\n    implementation: implementation\n  };\n}\nvar ReactSharedInternals =\n  React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\nfunction getCrossOriginStringAs(as, input) {\n  if (\"font\" === as) return \"\";\n  if (\"string\" === typeof input)\n    return \"use-credentials\" === input ? input : \"\";\n}\nexports.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE =\n  Internals;\nexports.createPortal = function (children, container) {\n  var key =\n    2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : null;\n  if (\n    !container ||\n    (1 !== container.nodeType &&\n      9 !== container.nodeType &&\n      11 !== container.nodeType)\n  )\n    throw Error(formatProdErrorMessage(299));\n  return createPortal$1(children, container, null, key);\n};\nexports.flushSync = function (fn) {\n  var previousTransition = ReactSharedInternals.T,\n    previousUpdatePriority = Internals.p;\n  try {\n    if (((ReactSharedInternals.T = null), (Internals.p = 2), fn)) return fn();\n  } finally {\n    (ReactSharedInternals.T = previousTransition),\n      (Internals.p = previousUpdatePriority),\n      Internals.d.f();\n  }\n};\nexports.preconnect = function (href, options) {\n  \"string\" === typeof href &&\n    (options\n      ? ((options = options.crossOrigin),\n        (options =\n          \"string\" === typeof options\n            ? \"use-credentials\" === options\n              ? options\n              : \"\"\n            : void 0))\n      : (options = null),\n    Internals.d.C(href, options));\n};\nexports.prefetchDNS = function (href) {\n  \"string\" === typeof href && Internals.d.D(href);\n};\nexports.preinit = function (href, options) {\n  if (\"string\" === typeof href && options && \"string\" === typeof options.as) {\n    var as = options.as,\n      crossOrigin = getCrossOriginStringAs(as, options.crossOrigin),\n      integrity =\n        \"string\" === typeof options.integrity ? options.integrity : void 0,\n      fetchPriority =\n        \"string\" === typeof options.fetchPriority\n          ? options.fetchPriority\n          : void 0;\n    \"style\" === as\n      ? Internals.d.S(\n          href,\n          \"string\" === typeof options.precedence ? options.precedence : void 0,\n          {\n            crossOrigin: crossOrigin,\n            integrity: integrity,\n            fetchPriority: fetchPriority\n          }\n        )\n      : \"script\" === as &&\n        Internals.d.X(href, {\n          crossOrigin: crossOrigin,\n          integrity: integrity,\n          fetchPriority: fetchPriority,\n          nonce: \"string\" === typeof options.nonce ? options.nonce : void 0\n        });\n  }\n};\nexports.preinitModule = function (href, options) {\n  if (\"string\" === typeof href)\n    if (\"object\" === typeof options && null !== options) {\n      if (null == options.as || \"script\" === options.as) {\n        var crossOrigin = getCrossOriginStringAs(\n          options.as,\n          options.crossOrigin\n        );\n        Internals.d.M(href, {\n          crossOrigin: crossOrigin,\n          integrity:\n            \"string\" === typeof options.integrity ? options.integrity : void 0,\n          nonce: \"string\" === typeof options.nonce ? options.nonce : void 0\n        });\n      }\n    } else null == options && Internals.d.M(href);\n};\nexports.preload = function (href, options) {\n  if (\n    \"string\" === typeof href &&\n    \"object\" === typeof options &&\n    null !== options &&\n    \"string\" === typeof options.as\n  ) {\n    var as = options.as,\n      crossOrigin = getCrossOriginStringAs(as, options.crossOrigin);\n    Internals.d.L(href, as, {\n      crossOrigin: crossOrigin,\n      integrity:\n        \"string\" === typeof options.integrity ? options.integrity : void 0,\n      nonce: \"string\" === typeof options.nonce ? options.nonce : void 0,\n      type: \"string\" === typeof options.type ? options.type : void 0,\n      fetchPriority:\n        \"string\" === typeof options.fetchPriority\n          ? options.fetchPriority\n          : void 0,\n      referrerPolicy:\n        \"string\" === typeof options.referrerPolicy\n          ? options.referrerPolicy\n          : void 0,\n      imageSrcSet:\n        \"string\" === typeof options.imageSrcSet ? options.imageSrcSet : void 0,\n      imageSizes:\n        \"string\" === typeof options.imageSizes ? options.imageSizes : void 0,\n      media: \"string\" === typeof options.media ? options.media : void 0\n    });\n  }\n};\nexports.preloadModule = function (href, options) {\n  if (\"string\" === typeof href)\n    if (options) {\n      var crossOrigin = getCrossOriginStringAs(options.as, options.crossOrigin);\n      Internals.d.m(href, {\n        as:\n          \"string\" === typeof options.as && \"script\" !== options.as\n            ? options.as\n            : void 0,\n        crossOrigin: crossOrigin,\n        integrity:\n          \"string\" === typeof options.integrity ? options.integrity : void 0\n      });\n    } else Internals.d.m(href);\n};\nexports.requestFormReset = function (form) {\n  Internals.d.r(form);\n};\nexports.unstable_batchedUpdates = function (fn, a) {\n  return fn(a);\n};\nexports.useFormState = function (action, initialState, permalink) {\n  return ReactSharedInternals.H.useFormState(action, initialState, permalink);\n};\nexports.useFormStatus = function () {\n  return ReactSharedInternals.H.useHostTransitionStatus();\n};\nexports.version = \"19.1.1\";\n", "'use strict';\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' ||\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function'\n  ) {\n    return;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\n\nif (process.env.NODE_ENV === 'production') {\n  // DCE check should happen before ReactDOM bundle executes so that\n  // DevTools can report bad minification during injection.\n  checkDCE();\n  module.exports = require('./cjs/react-dom.production.js');\n} else {\n  module.exports = require('./cjs/react-dom.development.js');\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAWA,QAAI,QAAQ;AACZ,aAAS,uBAAuB,MAAM;AACpC,UAAI,MAAM,8BAA8B;AACxC,UAAI,IAAI,UAAU,QAAQ;AACxB,eAAO,aAAa,mBAAmB,UAAU,CAAC,CAAC;AACnD,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AACpC,iBAAO,aAAa,mBAAmB,UAAU,CAAC,CAAC;AAAA,MACvD;AACA,aACE,2BACA,OACA,aACA,MACA;AAAA,IAEJ;AACA,aAAS,OAAO;AAAA,IAAC;AACjB,QAAI,YAAY;AAAA,MACZ,GAAG;AAAA,QACD,GAAG;AAAA,QACH,GAAG,WAAY;AACb,gBAAM,MAAM,uBAAuB,GAAG,CAAC;AAAA,QACzC;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,GAAG;AAAA,MACH,aAAa;AAAA,IACf;AAhBF,QAiBE,oBAAoB,OAAO,IAAI,cAAc;AAC/C,aAAS,eAAe,UAAU,eAAe,gBAAgB;AAC/D,UAAI,MACF,IAAI,UAAU,UAAU,WAAW,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AACnE,aAAO;AAAA,QACL,UAAU;AAAA,QACV,KAAK,QAAQ,MAAM,OAAO,KAAK;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,uBACF,MAAM;AACR,aAAS,uBAAuB,IAAI,OAAO;AACzC,UAAI,WAAW,GAAI,QAAO;AAC1B,UAAI,aAAa,OAAO;AACtB,eAAO,sBAAsB,QAAQ,QAAQ;AAAA,IACjD;AACA,YAAQ,+DACN;AACF,YAAQ,eAAe,SAAU,UAAU,WAAW;AACpD,UAAI,MACF,IAAI,UAAU,UAAU,WAAW,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AACnE,UACE,CAAC,aACA,MAAM,UAAU,YACf,MAAM,UAAU,YAChB,OAAO,UAAU;AAEnB,cAAM,MAAM,uBAAuB,GAAG,CAAC;AACzC,aAAO,eAAe,UAAU,WAAW,MAAM,GAAG;AAAA,IACtD;AACA,YAAQ,YAAY,SAAU,IAAI;AAChC,UAAI,qBAAqB,qBAAqB,GAC5C,yBAAyB,UAAU;AACrC,UAAI;AACF,YAAM,qBAAqB,IAAI,MAAQ,UAAU,IAAI,GAAI,GAAK,QAAO,GAAG;AAAA,MAC1E,UAAE;AACA,QAAC,qBAAqB,IAAI,oBACvB,UAAU,IAAI,wBACf,UAAU,EAAE,EAAE;AAAA,MAClB;AAAA,IACF;AACA,YAAQ,aAAa,SAAU,MAAM,SAAS;AAC5C,mBAAa,OAAO,SACjB,WACK,UAAU,QAAQ,aACnB,UACC,aAAa,OAAO,UAChB,sBAAsB,UACpB,UACA,KACF,UACL,UAAU,MACf,UAAU,EAAE,EAAE,MAAM,OAAO;AAAA,IAC/B;AACA,YAAQ,cAAc,SAAU,MAAM;AACpC,mBAAa,OAAO,QAAQ,UAAU,EAAE,EAAE,IAAI;AAAA,IAChD;AACA,YAAQ,UAAU,SAAU,MAAM,SAAS;AACzC,UAAI,aAAa,OAAO,QAAQ,WAAW,aAAa,OAAO,QAAQ,IAAI;AACzE,YAAI,KAAK,QAAQ,IACf,cAAc,uBAAuB,IAAI,QAAQ,WAAW,GAC5D,YACE,aAAa,OAAO,QAAQ,YAAY,QAAQ,YAAY,QAC9D,gBACE,aAAa,OAAO,QAAQ,gBACxB,QAAQ,gBACR;AACR,oBAAY,KACR,UAAU,EAAE;AAAA,UACV;AAAA,UACA,aAAa,OAAO,QAAQ,aAAa,QAAQ,aAAa;AAAA,UAC9D;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,IACA,aAAa,MACb,UAAU,EAAE,EAAE,MAAM;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,aAAa,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,QAC7D,CAAC;AAAA,MACP;AAAA,IACF;AACA,YAAQ,gBAAgB,SAAU,MAAM,SAAS;AAC/C,UAAI,aAAa,OAAO;AACtB,YAAI,aAAa,OAAO,WAAW,SAAS,SAAS;AACnD,cAAI,QAAQ,QAAQ,MAAM,aAAa,QAAQ,IAAI;AACjD,gBAAI,cAAc;AAAA,cAChB,QAAQ;AAAA,cACR,QAAQ;AAAA,YACV;AACA,sBAAU,EAAE,EAAE,MAAM;AAAA,cAClB;AAAA,cACA,WACE,aAAa,OAAO,QAAQ,YAAY,QAAQ,YAAY;AAAA,cAC9D,OAAO,aAAa,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,YAC7D,CAAC;AAAA,UACH;AAAA,QACF,MAAO,SAAQ,WAAW,UAAU,EAAE,EAAE,IAAI;AAAA,IAChD;AACA,YAAQ,UAAU,SAAU,MAAM,SAAS;AACzC,UACE,aAAa,OAAO,QACpB,aAAa,OAAO,WACpB,SAAS,WACT,aAAa,OAAO,QAAQ,IAC5B;AACA,YAAI,KAAK,QAAQ,IACf,cAAc,uBAAuB,IAAI,QAAQ,WAAW;AAC9D,kBAAU,EAAE,EAAE,MAAM,IAAI;AAAA,UACtB;AAAA,UACA,WACE,aAAa,OAAO,QAAQ,YAAY,QAAQ,YAAY;AAAA,UAC9D,OAAO,aAAa,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,UAC3D,MAAM,aAAa,OAAO,QAAQ,OAAO,QAAQ,OAAO;AAAA,UACxD,eACE,aAAa,OAAO,QAAQ,gBACxB,QAAQ,gBACR;AAAA,UACN,gBACE,aAAa,OAAO,QAAQ,iBACxB,QAAQ,iBACR;AAAA,UACN,aACE,aAAa,OAAO,QAAQ,cAAc,QAAQ,cAAc;AAAA,UAClE,YACE,aAAa,OAAO,QAAQ,aAAa,QAAQ,aAAa;AAAA,UAChE,OAAO,aAAa,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,QAC7D,CAAC;AAAA,MACH;AAAA,IACF;AACA,YAAQ,gBAAgB,SAAU,MAAM,SAAS;AAC/C,UAAI,aAAa,OAAO;AACtB,YAAI,SAAS;AACX,cAAI,cAAc,uBAAuB,QAAQ,IAAI,QAAQ,WAAW;AACxE,oBAAU,EAAE,EAAE,MAAM;AAAA,YAClB,IACE,aAAa,OAAO,QAAQ,MAAM,aAAa,QAAQ,KACnD,QAAQ,KACR;AAAA,YACN;AAAA,YACA,WACE,aAAa,OAAO,QAAQ,YAAY,QAAQ,YAAY;AAAA,UAChE,CAAC;AAAA,QACH,MAAO,WAAU,EAAE,EAAE,IAAI;AAAA,IAC7B;AACA,YAAQ,mBAAmB,SAAU,MAAM;AACzC,gBAAU,EAAE,EAAE,IAAI;AAAA,IACpB;AACA,YAAQ,0BAA0B,SAAU,IAAI,GAAG;AACjD,aAAO,GAAG,CAAC;AAAA,IACb;AACA,YAAQ,eAAe,SAAU,QAAQ,cAAc,WAAW;AAChE,aAAO,qBAAqB,EAAE,aAAa,QAAQ,cAAc,SAAS;AAAA,IAC5E;AACA,YAAQ,gBAAgB,WAAY;AAClC,aAAO,qBAAqB,EAAE,wBAAwB;AAAA,IACxD;AACA,YAAQ,UAAU;AAAA;AAAA;;;ACjNlB;AAAA;AAEA,aAAS,WAAW;AAElB,UACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,aAAa,YACnD;AACA;AAAA,MACF;AACA,UAAI,OAAuC;AAQzC,cAAM,IAAI,MAAM,KAAK;AAAA,MACvB;AACA,UAAI;AAEF,uCAA+B,SAAS,QAAQ;AAAA,MAClD,SAAS,KAAK;AAGZ,gBAAQ,MAAM,GAAG;AAAA,MACnB;AAAA,IACF;AAEA,QAAI,MAAuC;AAGzC,eAAS;AACT,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": []}