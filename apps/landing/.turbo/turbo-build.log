
> landing@0.0.1 build /Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/apps/landing
> astro build

22:14:11 [content] Syncing content
22:14:11 [content] Synced content
22:14:11 [types] Generated 26ms
22:14:11 [build] output: "static"
22:14:11 [build] mode: "static"
22:14:11 [build] directory: /Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/apps/landing/dist/
22:14:11 [build] Collecting build info...
22:14:11 [build] ✓ Completed in 40ms.
22:14:11 [build] Building static entrypoints...
22:14:11 [vite] ✓ built in 394ms
22:14:11 [build] ✓ Completed in 415ms.

 building client (vite) 
22:14:11 [vite] transforming...
22:14:12 [vite] ✓ 23 modules transformed.
22:14:12 [vite] rendering chunks...
22:14:12 [vite] computing gzip size...
22:14:12 [vite] dist/_astro/Button.astro_astro_type_script_index_0_lang.BHE08Xts.js   10.84 kB │ gzip:  4.37 kB
22:14:12 [vite] dist/_astro/client.DpgqHGsl.js                                       187.44 kB │ gzip: 59.07 kB
22:14:12 [vite] ✓ built in 372ms

 generating static routes 
22:14:12 ▶ src/pages/markdown-page.md
22:14:12   └─ /markdown-page/index.html (+4ms) 
22:14:12 ▶ src/pages/index.astro
22:14:12   └─ /index.html (+1ms) 
22:14:12 ✓ Completed in 46ms.

22:14:12 [build] 2 page(s) built in 884ms
22:14:12 [build] Complete!
