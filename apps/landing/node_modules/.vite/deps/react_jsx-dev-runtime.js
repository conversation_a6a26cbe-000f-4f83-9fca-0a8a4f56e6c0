import {
  __commonJS
} from "./chunk-5WRI5ZAA.js";

// ../../node_modules/.pnpm/react@19.1.1/node_modules/react/cjs/react-jsx-dev-runtime.production.js
var require_react_jsx_dev_runtime_production = __commonJS({
  "../../node_modules/.pnpm/react@19.1.1/node_modules/react/cjs/react-jsx-dev-runtime.production.js"(exports) {
    "use strict";
    var REACT_FRAGMENT_TYPE = Symbol.for("react.fragment");
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = void 0;
  }
});

// ../../node_modules/.pnpm/react@19.1.1/node_modules/react/jsx-dev-runtime.js
var require_jsx_dev_runtime = __commonJS({
  "../../node_modules/.pnpm/react@19.1.1/node_modules/react/jsx-dev-runtime.js"(exports, module) {
    if (true) {
      module.exports = require_react_jsx_dev_runtime_production();
    } else {
      module.exports = null;
    }
  }
});
export default require_jsx_dev_runtime();
/*! Bundled license information:

react/cjs/react-jsx-dev-runtime.production.js:
  (**
   * @license React
   * react-jsx-dev-runtime.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=react_jsx-dev-runtime.js.map
