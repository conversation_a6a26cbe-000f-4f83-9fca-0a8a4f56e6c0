{"version": 3, "sources": ["../../../../../node_modules/.pnpm/react@19.1.1/node_modules/react/cjs/react-jsx-runtime.production.js", "../../../../../node_modules/.pnpm/react@19.1.1/node_modules/react/jsx-runtime.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config)\n      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAWA,QAAI,qBAAqB,OAAO,IAAI,4BAA4B;AAAhE,QACE,sBAAsB,OAAO,IAAI,gBAAgB;AACnD,aAAS,QAAQ,MAAM,QAAQ,UAAU;AACvC,UAAI,MAAM;AACV,iBAAW,aAAa,MAAM,KAAK;AACnC,iBAAW,OAAO,QAAQ,MAAM,KAAK,OAAO;AAC5C,UAAI,SAAS,QAAQ;AACnB,mBAAW,CAAC;AACZ,iBAAS,YAAY;AACnB,oBAAU,aAAa,SAAS,QAAQ,IAAI,OAAO,QAAQ;AAAA,MAC/D,MAAO,YAAW;AAClB,eAAS,SAAS;AAClB,aAAO;AAAA,QACL,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,KAAK,WAAW,SAAS,SAAS;AAAA,QAClC,OAAO;AAAA,MACT;AAAA,IACF;AACA,YAAQ,WAAW;AACnB,YAAQ,MAAM;AACd,YAAQ,OAAO;AAAA;AAAA;;;ACjCf;AAAA;AAEA,QAAI,MAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": []}