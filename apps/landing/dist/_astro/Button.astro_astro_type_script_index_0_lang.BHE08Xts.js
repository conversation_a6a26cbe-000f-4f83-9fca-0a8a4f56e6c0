var pr=(x,m)=>()=>(m||x((m={exports:{}}).exports,m),m.exports);var br=pr((Cr,I)=>{var I={};(function x(m,T,k,P){var A=!!(m.Worker&&m.Blob&&m.Promise&&m.OffscreenCanvas&&m.OffscreenCanvasRenderingContext2D&&m.HTMLCanvasElement&&m.HTMLCanvasElement.prototype.transferControlToOffscreen&&m.URL&&m.URL.createObjectURL),L=typeof Path2D=="function"&&typeof DOMMatrix=="function",_=function(){if(!m.OffscreenCanvas)return!1;var e=new OffscreenCanvas(1,1),r=e.getContext("2d");r.fillRect(0,0,1,1);var a=e.transferToImageBitmap();try{r.createPattern(a,"no-repeat")}catch{return!1}return!0}();function N(){}function E(e){var r=T.exports.Promise,a=r!==void 0?r:m.Promise;return typeof a=="function"?new a(e):(e(N,N),null)}var F=function(e,r){return{transform:function(a){if(e)return a;if(r.has(a))return r.get(a);var t=new OffscreenCanvas(a.width,a.height),o=t.getContext("2d");return o.drawImage(a,0,0),r.set(a,t),t},clear:function(){r.clear()}}}(_,new Map),S=function(){var e=Math.floor(16.666666666666668),r,a,t={},o=0;return typeof requestAnimationFrame=="function"&&typeof cancelAnimationFrame=="function"?(r=function(i){var l=Math.random();return t[l]=requestAnimationFrame(function n(s){o===s||o+e-1<s?(o=s,delete t[l],i()):t[l]=requestAnimationFrame(n)}),l},a=function(i){t[i]&&cancelAnimationFrame(t[i])}):(r=function(i){return setTimeout(i,e)},a=function(i){return clearTimeout(i)}),{frame:r,cancel:a}}(),q=function(){var e,r,a={};function t(o){function i(l,n){o.postMessage({options:l||{},callback:n})}o.init=function(n){var s=n.transferControlToOffscreen();o.postMessage({canvas:s},[s])},o.fire=function(n,s,u){if(r)return i(n,null),r;var d=Math.random().toString(36).slice(2);return r=E(function(h){function f(g){g.data.callback===d&&(delete a[d],o.removeEventListener("message",f),r=null,F.clear(),u(),h())}o.addEventListener("message",f),i(n,d),a[d]=f.bind(null,{data:{callback:d}})}),r},o.reset=function(){o.postMessage({reset:!0});for(var n in a)a[n](),delete a[n]}}return function(){if(e)return e;if(!k&&A){var o=["var CONFETTI, SIZE = {}, module = {};","("+x.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join(`
`);try{e=new Worker(URL.createObjectURL(new Blob([o])))}catch(i){return typeof console!==void 0&&typeof console.warn=="function"&&console.warn("🎊 Could not load worker",i),null}t(e)}return e}}(),H={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function G(e,r){return r?r(e):e}function J(e){return e!=null}function v(e,r,a){return G(e&&J(e[r])?e[r]:H[r],a)}function K(e){return e<0?0:Math.floor(e)}function Q(e,r){return Math.floor(Math.random()*(r-e))+e}function O(e){return parseInt(e,16)}function $(e){return e.map(X)}function X(e){var r=String(e).replace(/[^0-9a-f]/gi,"");return r.length<6&&(r=r[0]+r[0]+r[1]+r[1]+r[2]+r[2]),{r:O(r.substring(0,2)),g:O(r.substring(2,4)),b:O(r.substring(4,6))}}function Y(e){var r=v(e,"origin",Object);return r.x=v(r,"x",Number),r.y=v(r,"y",Number),r}function rr(e){e.width=document.documentElement.clientWidth,e.height=document.documentElement.clientHeight}function er(e){var r=e.getBoundingClientRect();e.width=r.width,e.height=r.height}function ar(e){var r=document.createElement("canvas");return r.style.position="fixed",r.style.top="0px",r.style.left="0px",r.style.pointerEvents="none",r.style.zIndex=e,r}function nr(e,r,a,t,o,i,l,n,s){e.save(),e.translate(r,a),e.rotate(i),e.scale(t,o),e.arc(0,0,1,l,n,s),e.restore()}function tr(e){var r=e.angle*(Math.PI/180),a=e.spread*(Math.PI/180);return{x:e.x,y:e.y,wobble:Math.random()*10,wobbleSpeed:Math.min(.11,Math.random()*.1+.05),velocity:e.startVelocity*.5+Math.random()*e.startVelocity,angle2D:-r+(.5*a-Math.random()*a),tiltAngle:(Math.random()*(.75-.25)+.25)*Math.PI,color:e.color,shape:e.shape,tick:0,totalTicks:e.ticks,decay:e.decay,drift:e.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:e.gravity*3,ovalScalar:.6,scalar:e.scalar,flat:e.flat}}function or(e,r){r.x+=Math.cos(r.angle2D)*r.velocity+r.drift,r.y+=Math.sin(r.angle2D)*r.velocity+r.gravity,r.velocity*=r.decay,r.flat?(r.wobble=0,r.wobbleX=r.x+10*r.scalar,r.wobbleY=r.y+10*r.scalar,r.tiltSin=0,r.tiltCos=0,r.random=1):(r.wobble+=r.wobbleSpeed,r.wobbleX=r.x+10*r.scalar*Math.cos(r.wobble),r.wobbleY=r.y+10*r.scalar*Math.sin(r.wobble),r.tiltAngle+=.1,r.tiltSin=Math.sin(r.tiltAngle),r.tiltCos=Math.cos(r.tiltAngle),r.random=Math.random()+2);var a=r.tick++/r.totalTicks,t=r.x+r.random*r.tiltCos,o=r.y+r.random*r.tiltSin,i=r.wobbleX+r.random*r.tiltCos,l=r.wobbleY+r.random*r.tiltSin;if(e.fillStyle="rgba("+r.color.r+", "+r.color.g+", "+r.color.b+", "+(1-a)+")",e.beginPath(),L&&r.shape.type==="path"&&typeof r.shape.path=="string"&&Array.isArray(r.shape.matrix))e.fill(lr(r.shape.path,r.shape.matrix,r.x,r.y,Math.abs(i-t)*.1,Math.abs(l-o)*.1,Math.PI/10*r.wobble));else if(r.shape.type==="bitmap"){var n=Math.PI/10*r.wobble,s=Math.abs(i-t)*.1,u=Math.abs(l-o)*.1,d=r.shape.bitmap.width*r.scalar,h=r.shape.bitmap.height*r.scalar,f=new DOMMatrix([Math.cos(n)*s,Math.sin(n)*s,-Math.sin(n)*u,Math.cos(n)*u,r.x,r.y]);f.multiplySelf(new DOMMatrix(r.shape.matrix));var g=e.createPattern(F.transform(r.shape.bitmap),"no-repeat");g.setTransform(f),e.globalAlpha=1-a,e.fillStyle=g,e.fillRect(r.x-d/2,r.y-h/2,d,h),e.globalAlpha=1}else if(r.shape==="circle")e.ellipse?e.ellipse(r.x,r.y,Math.abs(i-t)*r.ovalScalar,Math.abs(l-o)*r.ovalScalar,Math.PI/10*r.wobble,0,2*Math.PI):nr(e,r.x,r.y,Math.abs(i-t)*r.ovalScalar,Math.abs(l-o)*r.ovalScalar,Math.PI/10*r.wobble,0,2*Math.PI);else if(r.shape==="star")for(var c=Math.PI/2*3,M=4*r.scalar,p=8*r.scalar,y=r.x,w=r.y,C=5,b=Math.PI/C;C--;)y=r.x+Math.cos(c)*p,w=r.y+Math.sin(c)*p,e.lineTo(y,w),c+=b,y=r.x+Math.cos(c)*M,w=r.y+Math.sin(c)*M,e.lineTo(y,w),c+=b;else e.moveTo(Math.floor(r.x),Math.floor(r.y)),e.lineTo(Math.floor(r.wobbleX),Math.floor(o)),e.lineTo(Math.floor(i),Math.floor(l)),e.lineTo(Math.floor(t),Math.floor(r.wobbleY));return e.closePath(),e.fill(),r.tick<r.totalTicks}function ir(e,r,a,t,o){var i=r.slice(),l=e.getContext("2d"),n,s,u=E(function(d){function h(){n=s=null,l.clearRect(0,0,t.width,t.height),F.clear(),o(),d()}function f(){k&&!(t.width===P.width&&t.height===P.height)&&(t.width=e.width=P.width,t.height=e.height=P.height),!t.width&&!t.height&&(a(e),t.width=e.width,t.height=e.height),l.clearRect(0,0,t.width,t.height),i=i.filter(function(g){return or(l,g)}),i.length?n=S.frame(f):h()}n=S.frame(f),s=h});return{addFettis:function(d){return i=i.concat(d),u},canvas:e,promise:u,reset:function(){n&&S.cancel(n),s&&s()}}}function W(e,r){var a=!e,t=!!v(r||{},"resize"),o=!1,i=v(r,"disableForReducedMotion",Boolean),l=A&&!!v(r||{},"useWorker"),n=l?q():null,s=a?rr:er,u=e&&n?!!e.__confetti_initialized:!1,d=typeof matchMedia=="function"&&matchMedia("(prefers-reduced-motion)").matches,h;function f(c,M,p){for(var y=v(c,"particleCount",K),w=v(c,"angle",Number),C=v(c,"spread",Number),b=v(c,"startVelocity",Number),ur=v(c,"decay",Number),hr=v(c,"gravity",Number),dr=v(c,"drift",Number),D=v(c,"colors",$),fr=v(c,"ticks",Number),U=v(c,"shapes"),vr=v(c,"scalar"),mr=!!v(c,"flat"),z=Y(c),V=y,B=[],gr=e.width*z.x,Mr=e.height*z.y;V--;)B.push(tr({x:gr,y:Mr,angle:w,spread:C,startVelocity:b,color:D[V%D.length],shape:U[Q(0,U.length)],ticks:fr,decay:ur,gravity:hr,drift:dr,scalar:vr,flat:mr}));return h?h.addFettis(B):(h=ir(e,B,s,M,p),h.promise)}function g(c){var M=i||v(c,"disableForReducedMotion",Boolean),p=v(c,"zIndex",Number);if(M&&d)return E(function(b){b()});a&&h?e=h.canvas:a&&!e&&(e=ar(p),document.body.appendChild(e)),t&&!u&&s(e);var y={width:e.width,height:e.height};n&&!u&&n.init(e),u=!0,n&&(e.__confetti_initialized=!0);function w(){if(n){var b={getBoundingClientRect:function(){if(!a)return e.getBoundingClientRect()}};s(b),n.postMessage({resize:{width:b.width,height:b.height}});return}y.width=y.height=null}function C(){h=null,t&&(o=!1,m.removeEventListener("resize",w)),a&&e&&(document.body.contains(e)&&document.body.removeChild(e),e=null,u=!1)}return t&&!o&&(o=!0,m.addEventListener("resize",w,!1)),n?n.fire(c,y,C):f(c,y,C)}return g.reset=function(){n&&n.reset(),h&&h.reset()},g}var R;function j(){return R||(R=W(null,{useWorker:!0,resize:!0})),R}function lr(e,r,a,t,o,i,l){var n=new Path2D(e),s=new Path2D;s.addPath(n,new DOMMatrix(r));var u=new Path2D;return u.addPath(s,new DOMMatrix([Math.cos(l)*o,Math.sin(l)*o,-Math.sin(l)*i,Math.cos(l)*i,a,t])),u}function sr(e){if(!L)throw new Error("path confetti are not supported in this browser");var r,a;typeof e=="string"?r=e:(r=e.path,a=e.matrix);var t=new Path2D(r),o=document.createElement("canvas"),i=o.getContext("2d");if(!a){for(var l=1e3,n=l,s=l,u=0,d=0,h,f,g=0;g<l;g+=2)for(var c=0;c<l;c+=2)i.isPointInPath(t,g,c,"nonzero")&&(n=Math.min(n,g),s=Math.min(s,c),u=Math.max(u,g),d=Math.max(d,c));h=u-n,f=d-s;var M=10,p=Math.min(M/h,M/f);a=[p,0,0,p,-Math.round(h/2+n)*p,-Math.round(f/2+s)*p]}return{type:"path",path:r,matrix:a}}function cr(e){var r,a=1,t="#000000",o='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';typeof e=="string"?r=e:(r=e.text,a="scalar"in e?e.scalar:a,o="fontFamily"in e?e.fontFamily:o,t="color"in e?e.color:t);var i=10*a,l=""+i+"px "+o,n=new OffscreenCanvas(i,i),s=n.getContext("2d");s.font=l;var u=s.measureText(r),d=Math.ceil(u.actualBoundingBoxRight+u.actualBoundingBoxLeft),h=Math.ceil(u.actualBoundingBoxAscent+u.actualBoundingBoxDescent),f=2,g=u.actualBoundingBoxLeft+f,c=u.actualBoundingBoxAscent+f;d+=f+f,h+=f+f,n=new OffscreenCanvas(d,h),s=n.getContext("2d"),s.font=l,s.fillStyle=t,s.fillText(r,g,c);var M=1/a;return{type:"bitmap",bitmap:n.transferToImageBitmap(),matrix:[M,0,0,M,-d*M/2,-h*M/2]}}T.exports=function(){return j().apply(this,arguments)},T.exports.reset=function(){j().reset()},T.exports.create=W,T.exports.shapeFromPath=sr,T.exports.shapeFromText=cr})(function(){return typeof window<"u"?window:typeof self<"u"?self:this||{}}(),I,!1);const yr=I.exports;I.exports.create;const Z=document.body.querySelector("button");Z&&Z.addEventListener("click",()=>yr())});export default br();
