{"version": 3, "sources": ["../../../../../node_modules/.pnpm/react@19.1.1/node_modules/react/cjs/react-jsx-dev-runtime.production.js", "../../../../../node_modules/.pnpm/react@19.1.1/node_modules/react/jsx-dev-runtime.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = void 0;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAWA,QAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,YAAQ,WAAW;AACnB,YAAQ,SAAS;AAAA;AAAA;;;ACbjB;AAAA;AAEA,QAAI,MAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": []}